import { logger } from '@/utils/logger';
import { ParsedController } from '@/parser/ast-parser';

export interface PostmanCollection {
  info: {
    name: string;
    description: string;
    version: string;
    schema: string;
  };
  item: PostmanItem[];
  variable?: PostmanVariable[];
  auth?: PostmanAuth;
}

export interface PostmanItem {
  name: string;
  request?: PostmanRequest;
  item?: PostmanItem[];
  response?: PostmanResponse[];
  description?: string;
}

export interface PostmanRequest {
  method: string;
  header: PostmanHeader[];
  url: PostmanUrl;
  body?: PostmanBody | undefined;
  description?: string;
}

export interface PostmanUrl {
  raw: string;
  host: string[];
  path: string[];
  query?: PostmanQuery[] | undefined;
}

export interface PostmanHeader {
  key: string;
  value: string;
  description?: string;
  disabled?: boolean;
}

export interface PostmanQuery {
  key: string;
  value: string;
  description?: string;
  disabled?: boolean;
}

export interface PostmanBody {
  mode: 'raw' | 'formdata' | 'urlencoded';
  raw?: string;
  options?: {
    raw?: {
      language: string;
    };
  };
}

export interface PostmanResponse {
  name: string;
  originalRequest: PostmanRequest;
  status: string;
  code: number;
  header: PostmanHeader[];
  body: string;
}

export interface PostmanVariable {
  key: string;
  value: string;
  type: string;
  description?: string;
}

export interface PostmanAuth {
  type: string;
  bearer?: {
    token: string;
  };
  apikey?: {
    key: string;
    value: string;
    in: string;
  };
}

export class PostmanCollectionBuilder {
  private collection: PostmanCollection;
  private globalPrefix: string = '';

  constructor(name: string, description = '') {
    this.collection = {
      info: {
        name,
        description,
        version: '1.0.0',
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
      },
      item: [],
    };
  }

  /**
   * Build Postman collection from parsed controllers
   */
  buildFromControllers(controllers: ParsedController[]): PostmanCollection {
    logger.debug(`Building collection from ${controllers.length} controllers`, { module: 'builder' });

    // Clear existing items
    this.collection.item = [];

    // Add each controller as a folder
    for (const controller of controllers) {
      this.addController(controller);
    }

    logger.debug(`Built collection with ${this.collection.item.length} folders`, { module: 'builder' });
    return this.collection;
  }

  /**
   * Add a controller as a folder in the collection
   */
  addController(controller: ParsedController): void {
    logger.debug(`Adding controller: ${controller.name}`, { module: 'builder' });

    const folderItem: PostmanItem = {
      name: controller.name,
      item: [],
      description: this.buildControllerDescription(controller),
    };

    // Add each method as a request
    for (const method of controller.methods) {
      const requestItem = this.createRequestItem(method, controller);
      if (requestItem) {
        (folderItem.item as PostmanItem[]).push(requestItem);
      }
    }

    this.collection.item.push(folderItem);
  }

  /**
   * Build controller description
   */
  private buildControllerDescription(controller: ParsedController): string {
    let description = `Controller: ${controller.name}\n`;
    description += `Path: ${controller.path}\n`;

    if (controller.guards.length > 0) {
      description += `Guards: ${controller.guards.join(', ')}\n`;
    }

    if (controller.metadata['description']) {
      description += `\n${controller.metadata['description']}`;
    }

    return description;
  }

  /**
   * Create a request item from a parsed method
   */
  private createRequestItem(method: any, controller: ParsedController): PostmanItem | null {
    const fullPath = this.buildFullPath(controller.path, method.path);
    const url = this.buildUrl(fullPath, method.parameters);
    const headers = this.buildHeaders(method);
    const body = this.buildRequestBody(method.parameters);

    const request: PostmanRequest = {
      method: method.httpMethod,
      header: headers,
      url,
      body,
      description: method.description || `${method.httpMethod} ${fullPath}`,
    };

    // Add authentication if guards are present
    if (method.guards.length > 0 || controller.guards.length > 0) {
      // Auth will be handled at collection level
    }

    return {
      name: `${method.httpMethod} ${method.name}`,
      request,
      description: method.description,
    };
  }

  /**
   * Build full path from controller and method paths
   */
  private buildFullPath(controllerPath: string, methodPath: string): string {
    const controller = controllerPath.startsWith('/') ? controllerPath : `/${controllerPath}`;
    const method = methodPath.startsWith('/') ? methodPath : `/${methodPath}`;

    let fullPath = controller + method;

    // Clean up double slashes
    fullPath = fullPath.replace(/\/+/g, '/');

    // Ensure it starts with /
    if (!fullPath.startsWith('/')) {
      fullPath = '/' + fullPath;
    }

    return fullPath;
  }

  /**
   * Build Postman URL object
   */
  private buildUrl(path: string, parameters: any[]): PostmanUrl {
    const baseUrl = '{{baseUrl}}';

    // Build the complete path including global prefix
    let fullPath = path;
    if (this.globalPrefix && this.globalPrefix !== '') {
      // Ensure global prefix starts with / and doesn't end with /
      const normalizedPrefix = this.globalPrefix.startsWith('/')
        ? this.globalPrefix
        : `/${this.globalPrefix}`;
      const cleanPrefix = normalizedPrefix.endsWith('/')
        ? normalizedPrefix.slice(0, -1)
        : normalizedPrefix;

      // Ensure path starts with /
      const normalizedPath = path.startsWith('/') ? path : `/${path}`;

      fullPath = `${cleanPrefix}${normalizedPath}`;
    }

    const pathSegments = fullPath.split('/').filter(segment => segment.length > 0);

    // Replace path parameters with Postman variables
    const processedSegments = pathSegments.map(segment => {
      if (segment.startsWith(':')) {
        return `{{${segment.slice(1)}}}`;
      }
      return segment;
    });

    // Extract query parameters
    const queryParams: PostmanQuery[] = [];
    for (const param of parameters) {
      if (param.location === 'query') {
        queryParams.push({
          key: param.name,
          value: param.example || `{{${param.name}}}`,
          description: param.description,
          disabled: !param.required,
        });
      }
    }

    const finalPath = processedSegments.length > 0 ? `/${processedSegments.join('/')}` : '/';

    return {
      raw: `${baseUrl}${finalPath}`,
      host: [baseUrl],
      path: processedSegments,
      query: queryParams.length > 0 ? queryParams : undefined,
    };
  }

  /**
   * Build headers for request
   */
  private buildHeaders(method: any): PostmanHeader[] {
    const headers: PostmanHeader[] = [];

    // Add content-type for requests with body
    const hasBody = method.parameters.some((p: any) => p.location === 'body');
    if (hasBody) {
      headers.push({
        key: 'Content-Type',
        value: 'application/json',
        description: 'Request content type',
      });
    }

    // Add custom headers from parameters
    for (const param of method.parameters) {
      if (param.location === 'header') {
        headers.push({
          key: param.name,
          value: param.example || `{{${param.name}}}`,
          description: param.description,
          disabled: !param.required,
        });
      }
    }

    return headers;
  }

  /**
   * Build request body
   */
  private buildRequestBody(parameters: any[]): PostmanBody | undefined {
    const bodyParam = parameters.find(p => p.location === 'body');

    if (!bodyParam) {
      return undefined;
    }

    const bodyContent = bodyParam.schema || bodyParam.example || {};

    return {
      mode: 'raw',
      raw: JSON.stringify(bodyContent, null, 2),
      options: {
        raw: {
          language: 'json',
        },
      },
    };
  }

  /**
   * Set collection variables
   */
  setVariables(variables: Record<string, string>): void {
    this.collection.variable = Object.entries(variables).map(([key, value]) => ({
      key,
      value,
      type: 'string',
    }));
  }

  /**
   * Set global API prefix
   */
  setGlobalPrefix(prefix: string): void {
    this.globalPrefix = prefix;
    logger.debug(`Set global prefix: ${prefix}`, { module: 'builder' });
  }

  /**
   * Set collection authentication
   */
  setAuth(auth: PostmanAuth): void {
    this.collection.auth = auth;
  }

  /**
   * Get the built collection
   */
  getCollection(): PostmanCollection {
    return this.collection;
  }

  /**
   * Export collection as JSON string
   */
  toJSON(): string {
    return JSON.stringify(this.collection, null, 2);
  }
}
