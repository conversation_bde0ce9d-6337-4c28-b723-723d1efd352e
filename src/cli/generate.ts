import { Command } from 'commander';
import * as path from 'path';
import { logger } from '@/utils/logger';
import { loadConfig } from '@/config/config-loader';
import { ASTParser } from '@/parser/ast-parser';
import { PostmanCollectionBuilder } from '@/postman/builder';
import { StateManager } from '@/state/state-manager';
import { findControllerFiles, findControllerFilesAuto, generateFileHashes, writeFileWithDir, compareFileHashes } from '@/utils/file-utils';
import { detectGlobalPrefix } from '@/utils/prefix-detector';

export const generateCommand = new Command('generate')
  .description('Generate Postman collection from NestJS controllers')
  .option('-o, --output <file>', 'Output file path', 'postman-collection.json')
  .option('-f, --format', 'Format the output JSON', false)
  .option('--force', 'Force regeneration even if no changes detected', false)
  .action(async (options) => {
    try {
      logger.info('Starting collection generation', { module: 'generate' });

      // Load configuration
      const config = await loadConfig();
      logger.debug('Configuration loaded', { module: 'generate' });

      // Initialize state manager
      const stateManager = new StateManager();
      await stateManager.load();

      // Find controller files using smart discovery
      let controllerFiles: string[];

      if (config.nestjs.autoDiscovery) {
        console.log('🔍 Auto-discovering controllers across the entire project...');
        controllerFiles = await findControllerFilesAuto(
          config.nestjs.projectRoot,
          config.nestjs.excludePatterns
        );
      } else {
        console.log(`🔍 Searching for controllers in specified directories: ${Array.isArray(config.nestjs.srcDirs) ? config.nestjs.srcDirs.join(', ') : config.nestjs.srcDirs}...`);
        controllerFiles = await findControllerFiles(
          config.nestjs.srcDirs,
          config.nestjs.controllersPattern,
          config.nestjs.excludePatterns
        );
      }

      if (controllerFiles.length === 0) {
        logger.warn('No controller files found', { module: 'generate' });
        console.log('⚠️  No controller files found.');
        console.log('💡 Try one of these solutions:');
        if (config.nestjs.autoDiscovery) {
          console.log('   • Check if your controllers follow the *.controller.ts naming convention');
          console.log('   • Disable auto-discovery and specify directories manually in config');
        } else {
          console.log('   • Check your srcDirs configuration in postman.config.json');
          console.log('   • Enable auto-discovery to search the entire project');
        }
        return;
      }

      logger.info(`Found ${controllerFiles.length} controller files`, { module: 'generate' });

      // Show discovered directories for user feedback
      const directories = [...new Set(controllerFiles.map(file => path.dirname(file)))];
      console.log(`✅ Found controllers in ${directories.length} directories:`);
      directories.slice(0, 5).forEach(dir => console.log(`   📁 ${path.relative(process.cwd(), dir)}`));
      if (directories.length > 5) {
        console.log(`   ... and ${directories.length - 5} more directories`);
      }

      // Generate file hashes for change detection
      const currentHashes = await generateFileHashes(controllerFiles);
      const previousHashes = stateManager.getState().checksums;

      // Check for changes unless forced
      if (!options.force) {
        const changes = compareFileHashes(currentHashes, previousHashes);

        if (changes.added.length === 0 && changes.modified.length === 0 && changes.deleted.length === 0) {
          logger.info('No changes detected, skipping generation', { module: 'generate' });
          console.log('✅ No changes detected. Use --force to regenerate anyway.');
          return;
        }

        logger.info(`Changes detected: ${changes.added.length} added, ${changes.modified.length} modified, ${changes.deleted.length} deleted`, { module: 'generate' });
      }

      // Initialize parser
      const parser = new ASTParser({
        tsConfigPath: path.join(config.nestjs.projectRoot, 'tsconfig.json'),
        includePrivate: config.options.includePrivate,
        includeDeprecated: config.options.includeDeprecated,
      });

      // Parse controllers
      console.log('🔍 Parsing controllers...');
      const controllers = await parser.parseControllers(controllerFiles);

      if (controllers.length === 0) {
        logger.warn('No valid controllers found', { module: 'generate' });
        console.log('⚠️  No valid controllers found. Check your controller files.');
        return;
      }

      logger.info(`Parsed ${controllers.length} controllers`, { module: 'generate' });
      console.log(`✅ Parsed ${controllers.length} controllers`);

      // Detect global prefix
      console.log('🔍 Detecting global API prefix...');
      const globalPrefixResult = await detectGlobalPrefix(
        config.nestjs.projectRoot,
        config.nestjs.globalPrefix
      );

      console.log(`📍 Global prefix: "${globalPrefixResult.prefix}" (${globalPrefixResult.source}, confidence: ${globalPrefixResult.confidence})`);
      if (globalPrefixResult.detectedFrom) {
        console.log(`   Detected from: ${globalPrefixResult.detectedFrom}`);
      }

      // Build collection
      console.log('🏗️  Building Postman collection...');
      const builder = new PostmanCollectionBuilder(
        'NestJS API Collection',
        'Auto-generated collection from NestJS controllers'
      );

      // Set global prefix
      builder.setGlobalPrefix(globalPrefixResult.prefix);

      // Set default variables
      builder.setVariables({
        baseUrl: 'http://localhost:3000',
        globalPrefix: globalPrefixResult.prefix,
      });

      // Set default authentication (Bearer token)
      builder.setAuth({
        type: 'bearer',
        bearer: {
          token: '{{jwt_token}}',
        },
      });

      const collection = builder.buildFromControllers(controllers);

      // Write output file
      const outputPath = path.resolve(options.output);
      const jsonContent = options.format
        ? JSON.stringify(collection, null, 2)
        : JSON.stringify(collection);

      await writeFileWithDir(outputPath, jsonContent);

      // Update state
      for (const [filePath, hash] of Object.entries(currentHashes)) {
        stateManager.updateFileState(filePath, hash);
      }
      stateManager.updateLastSync();
      await stateManager.save();

      // Success message
      const totalEndpoints = controllers.reduce((sum, controller) => sum + controller.methods.length, 0);
      console.log(`✅ Generated collection with ${totalEndpoints} endpoints`);
      console.log(`📄 Output: ${outputPath}`);

      logger.info('Collection generation completed successfully', {
        module: 'generate',
        context: {
          controllers: controllers.length,
          endpoints: totalEndpoints,
          outputPath,
        }
      });

    } catch (error) {
      logger.error('Generate command failed', error as Error, { module: 'generate' });
      console.error('❌ Generation failed:', (error as Error).message);
      process.exit(1);
    }
  });
