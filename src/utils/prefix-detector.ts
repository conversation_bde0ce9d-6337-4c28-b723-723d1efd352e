import * as fs from 'fs-extra';
import * as path from 'path';
import { Project, SourceFile } from 'ts-morph';
import { logger } from '@/utils/logger';

export interface GlobalPrefixDetectionResult {
  prefix: string;
  source: 'detected' | 'config' | 'default';
  confidence: 'high' | 'medium' | 'low';
  detectedFrom?: string;
}

export class GlobalPrefixDetector {
  private project: Project;

  constructor(projectRoot: string) {
    this.project = new Project({
      tsConfigFilePath: path.join(projectRoot, 'tsconfig.json'),
      skipAddingFilesFromTsConfig: true,
      useInMemoryFileSystem: false,
    });
  }

  /**
   * Detect global prefix from NestJS application
   */
  async detectGlobalPrefix(
    projectRoot: string,
    configuredPrefix?: string
  ): Promise<GlobalPrefixDetectionResult> {
    logger.debug('Starting global prefix detection', { module: 'prefix-detector' });

    // If explicitly configured, use that
    if (configuredPrefix !== undefined && configuredPrefix !== '') {
      logger.debug(`Using configured prefix: ${configuredPrefix}`, { module: 'prefix-detector' });
      return {
        prefix: configuredPrefix,
        source: 'config',
        confidence: 'high',
      };
    }

    // Try to detect from main.ts or app.ts files
    const detectedPrefix = await this.detectFromMainFiles(projectRoot);
    if (detectedPrefix) {
      return detectedPrefix;
    }

    // Try to detect from common patterns in controllers
    const controllerPrefix = await this.detectFromControllerPatterns(projectRoot);
    if (controllerPrefix) {
      return controllerPrefix;
    }

    // Default fallback
    logger.debug('No global prefix detected, using empty prefix', { module: 'prefix-detector' });
    return {
      prefix: '',
      source: 'default',
      confidence: 'low',
    };
  }

  /**
   * Detect global prefix from main.ts, app.ts, or bootstrap files
   */
  private async detectFromMainFiles(projectRoot: string): Promise<GlobalPrefixDetectionResult | null> {
    const mainFiles = [
      'src/main.ts',
      'src/app.ts',
      'src/bootstrap.ts',
      'main.ts',
      'app.ts',
      'bootstrap.ts',
    ];

    for (const mainFile of mainFiles) {
      const filePath = path.join(projectRoot, mainFile);
      
      if (await fs.pathExists(filePath)) {
        logger.debug(`Checking main file: ${filePath}`, { module: 'prefix-detector' });
        
        try {
          const sourceFile = this.project.addSourceFileAtPath(filePath);
          const prefix = this.extractGlobalPrefixFromFile(sourceFile);
          
          if (prefix) {
            logger.debug(`Detected global prefix "${prefix}" from ${mainFile}`, { module: 'prefix-detector' });
            return {
              prefix,
              source: 'detected',
              confidence: 'high',
              detectedFrom: mainFile,
            };
          }
        } catch (error) {
          logger.warn(`Failed to parse ${mainFile}`, { module: 'prefix-detector' });
        }
      }
    }

    return null;
  }

  /**
   * Extract global prefix from a source file
   */
  private extractGlobalPrefixFromFile(sourceFile: SourceFile): string | null {
    const text = sourceFile.getFullText();

    // Look for app.setGlobalPrefix() calls
    const globalPrefixRegex = /\.setGlobalPrefix\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
    let match;
    
    while ((match = globalPrefixRegex.exec(text)) !== null) {
      const prefix = match[1];
      if (prefix) {
        return prefix.startsWith('/') ? prefix : `/${prefix}`;
      }
    }

    // Look for ConfigService.get() calls that might contain API prefix
    const configRegex = /configService\.get\s*\(\s*['"`]([^'"`]*(?:api|prefix|base)[^'"`]*)['"`]\s*\)/gi;
    while ((match = configRegex.exec(text)) !== null) {
      const configKey = match[1];
      if (configKey && (configKey.toLowerCase().includes('prefix') || configKey.toLowerCase().includes('api'))) {
        // This is a potential prefix from config, but we can't determine the actual value
        // Return a common default
        return '/api';
      }
    }

    // Look for environment variable usage
    const envRegex = /process\.env\[?['"`]([^'"`]*(?:API|PREFIX|BASE)[^'"`]*)['"`]\]?/gi;
    while ((match = envRegex.exec(text)) !== null) {
      const envKey = match[1];
      if (envKey && (envKey.toLowerCase().includes('prefix') || envKey.toLowerCase().includes('api'))) {
        return '/api'; // Common default
      }
    }

    return null;
  }

  /**
   * Detect common prefix patterns from controller analysis
   */
  private async detectFromControllerPatterns(projectRoot: string): Promise<GlobalPrefixDetectionResult | null> {
    try {
      // Look for common controller patterns
      const controllerFiles = await this.findControllerFiles(projectRoot);
      const prefixCounts = new Map<string, number>();

      for (const filePath of controllerFiles.slice(0, 10)) { // Analyze first 10 controllers
        try {
          const sourceFile = this.project.addSourceFileAtPath(filePath);
          const classes = sourceFile.getClasses();

          for (const cls of classes) {
            const controllerDecorator = cls.getDecorators().find(d => d.getName() === 'Controller');
            if (controllerDecorator) {
              const args = controllerDecorator.getArguments();
              if (args.length > 0) {
                const pathArg = args[0];
                if (pathArg) {
                  const controllerPath = pathArg.getText().slice(1, -1); // Remove quotes

                  // Extract potential global prefix patterns
                  const segments = controllerPath.split('/').filter(s => s.length > 0);
                  if (segments.length > 0) {
                    const firstSegment = `/${segments[0]}`;
                    if (this.isLikelyGlobalPrefix(firstSegment)) {
                      prefixCounts.set(firstSegment, (prefixCounts.get(firstSegment) || 0) + 1);
                    }
                  }
                }
              }
            }
          }
        } catch (error) {
          // Skip files that can't be parsed
          continue;
        }
      }

      // Find the most common prefix
      if (prefixCounts.size > 0) {
        const sortedPrefixes = Array.from(prefixCounts.entries())
          .sort((a, b) => b[1] - a[1]);

        if (sortedPrefixes.length > 0) {
          const firstEntry = sortedPrefixes[0];
          if (firstEntry) {
            const mostCommonPrefix = firstEntry[0];
            const count = firstEntry[1];
            const confidence = count >= 3 ? 'medium' : 'low';

          logger.debug(`Detected common prefix pattern: ${mostCommonPrefix} (${count} occurrences)`, { module: 'prefix-detector' });

            return {
              prefix: mostCommonPrefix,
              source: 'detected',
              confidence,
              detectedFrom: 'controller-patterns',
            };
          }
        }
      }
    } catch (error) {
      logger.warn('Failed to analyze controller patterns', { module: 'prefix-detector' });
    }

    return null;
  }

  /**
   * Check if a path segment is likely a global prefix
   */
  private isLikelyGlobalPrefix(segment: string): boolean {
    const commonPrefixes = [
      '/api',
      '/v1',
      '/v2',
      '/v3',
      '/rest',
      '/graphql',
      '/app',
      '/service',
    ];

    return commonPrefixes.includes(segment.toLowerCase()) ||
           segment.match(/^\/v\d+$/) !== null ||
           segment.match(/^\/api/) !== null;
  }

  /**
   * Find controller files in the project
   */
  private async findControllerFiles(projectRoot: string): Promise<string[]> {
    const glob = require('glob');
    const pattern = path.join(projectRoot, '**/*.controller.ts');
    
    return new Promise((resolve, reject) => {
      glob(pattern, {
        ignore: [
          '**/node_modules/**',
          '**/dist/**',
          '**/build/**',
          '**/.git/**',
          '**/*.spec.ts',
          '**/*.test.ts',
        ],
        absolute: true,
      }, (err: any, files: string[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(files);
        }
      });
    });
  }

  /**
   * Clean up resources
   */
  dispose(): void {
    // Clean up ts-morph project
    this.project.getSourceFiles().forEach(sf => sf.forget());
  }
}

/**
 * Convenience function to detect global prefix
 */
export async function detectGlobalPrefix(
  projectRoot: string,
  configuredPrefix?: string
): Promise<GlobalPrefixDetectionResult> {
  const detector = new GlobalPrefixDetector(projectRoot);
  try {
    return await detector.detectGlobalPrefix(projectRoot, configuredPrefix);
  } finally {
    detector.dispose();
  }
}
